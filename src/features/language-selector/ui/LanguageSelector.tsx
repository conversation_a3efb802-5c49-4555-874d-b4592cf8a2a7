import { Typography } from '@components/typography';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@components/ui/dropdown-menu';
import { useLanguages } from '@entities/languages';
import { useZendeskWidget } from '@entities/support';
import { ChevronDownIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useToggle } from 'react-use';

import { cn } from '@/shared/utils/tailwind';

import { LANGUAGE_SELECTOR_TEST_KEYS } from '../config';

type LanguageSelectorProps = {
  className?: string;
};

export const LanguageSelector = ({ className }: LanguageSelectorProps) => {
  const { i18n } = useTranslation();

  const { changeZendeskLocale } = useZendeskWidget();

  const languages = useLanguages();
  const [isDropdownMenuOpen, handleDropdownMenuToggle] = useToggle(false);

  const handleLanguageChange = (lang: string) => async () => {
    await i18n.changeLanguage(lang);
    changeZendeskLocale(lang);
  };

  return (
    <DropdownMenu onOpenChange={handleDropdownMenuToggle}>
      <DropdownMenuTrigger
        className="outline-none"
        data-testid={LANGUAGE_SELECTOR_TEST_KEYS.TRIGGER}
      >
        <div className={cn('flex items-center gap-1.5', className)}>
          <Typography
            data-testid={LANGUAGE_SELECTOR_TEST_KEYS.SELECTED_LANGUAGE}
            affects="semibold"
            className="text-inherit"
          >
            {i18n.language.toUpperCase()}
          </Typography>
          <ChevronDownIcon
            className={cn(isDropdownMenuOpen && 'rotate-180 transform')}
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[7.5rem]">
        {languages.map((lang) => (
          <DropdownMenuCheckboxItem
            key={lang}
            arrowPosition="right"
            checked={i18n.language === lang}
            data-testid={`${LANGUAGE_SELECTOR_TEST_KEYS.ITEM}-${lang}`}
            onCheckedChange={handleLanguageChange(lang)}
          >
            {lang.toUpperCase()}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
