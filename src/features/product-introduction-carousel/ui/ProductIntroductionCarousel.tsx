import { BackgroundImage } from '@components/BackgroundImage';
import { ProgressDots } from '@components/ProgressDots';
import { Typography } from '@components/typography';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
} from '@components/ui/carousel';
import { Skeleton } from '@components/ui/skeleton';
import Autoplay from 'embla-carousel-autoplay';
import {
  type FC,
  lazy,
  type ReactNode,
  Suspense,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';

import { cn } from '@/shared/utils/tailwind';

import { useProductCarouselSlides } from '../hooks';
import type { ProductSlide } from '../types';

const SlideTitleWithData = lazy(() =>
  import('./ConsumerLoanSlideTitle').then(({ SlideTitleWithData }) => ({
    default: SlideTitleWithData,
  })),
);

type ProductIntroductionCarouselProps = {
  useSlides?: () => Array<ProductSlide>;
  after?: ReactNode;
};

export const ProductIntroductionCarousel: FC<
  ProductIntroductionCarouselProps
> = ({ useSlides = useProductCarouselSlides, after }) => {
  const carouselRef = useRef(null);

  const slides = useSlides();

  const [api, setApi] = useState<CarouselApi>();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [shownSlides, setShownSlides] = useState<Array<number>>([]);

  const onProgressDotClick = useCallback(
    (index: number) => () => {
      api?.scrollTo(index);
    },
    [api],
  );

  useEffect(() => {
    if (!api) {
      return;
    }

    const handleCurrentSlideSet = () => {
      setCurrentSlide(api.selectedScrollSnap());
    };

    const handleShowSlidesSet = () => {
      const prevSlide = api.previousScrollSnap();
      if (shownSlides.includes(prevSlide)) {
        return;
      }

      setShownSlides((prev) => [...prev, prevSlide]);
    };

    api.on('select', handleCurrentSlideSet);
    api.on('select', handleShowSlidesSet);

    return () => {
      api.off('select', handleCurrentSlideSet);
      api.off('select', handleShowSlidesSet);
    };
  }, [api, shownSlides]);

  useEffect(() => {
    if (!api) {
      return;
    }

    const currentRef = carouselRef.current;

    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        api?.plugins().autoplay.play();
      }
      if (!entry.isIntersecting) {
        api?.plugins().autoplay.stop();
      }
    });

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [api]);

  return (
    <Carousel
      ref={carouselRef}
      onMouseLeave={() => api?.plugins().autoplay.play()}
      opts={{ loop: true }}
      plugins={[Autoplay({ delay: 5000, stopOnMouseEnter: true })]}
      setApi={setApi}
    >
      <CarouselContent>
        {slides.map(
          ({ badge, title, description, imgSrc, getTitleWithData }, index) => (
            <CarouselItem key={badge} className="relative">
              <div className="flex size-full flex-col items-center justify-end overflow-hidden px-6 pt-24 pb-[14.5rem] md:mx-auto md:max-w-[30rem] md:justify-center md:px-0 md:py-20">
                <Typography
                  className={cn(
                    'relative z-10 w-fit rounded-full bg-primary-white/20 p-[8px] px-4 text-typography-white opacity-0 backdrop-blur-[2px]',
                    !shownSlides.includes(index) &&
                      currentSlide === index &&
                      'animate-ride-in-left',
                    shownSlides.includes(index) && 'animate-ride-in-left',
                  )}
                >
                  {badge}
                </Typography>
                {getTitleWithData ? (
                  <Suspense fallback={<Skeleton className="m-2 h-10 w-full" />}>
                    <SlideTitleWithData
                      getTitleWithData={getTitleWithData}
                      shownSlides={shownSlides}
                      index={index}
                      currentSlide={currentSlide}
                    />
                  </Suspense>
                ) : (
                  <Typography
                    className={cn(
                      'relative z-10 mt-[15px] text-center text-typography-white opacity-0',
                      !shownSlides.includes(index) &&
                        currentSlide === index &&
                        'animation-delay-200 animate-ride-in-right',
                      shownSlides.includes(index) && 'animate-ride-in-right',
                    )}
                    variant="l"
                  >
                    {title}
                  </Typography>
                )}
                <Typography
                  className={cn(
                    'relative z-10 mt-[15px] text-center text-typography-white opacity-0',
                    !shownSlides.includes(index) &&
                      currentSlide === index &&
                      'animation-delay-400 animate-ride-in-left',
                    shownSlides.includes(index) && 'animate-ride-in-left',
                  )}
                  variant="s"
                >
                  {description}
                </Typography>
              </div>
              <BackgroundImage
                coverClassName="bg-gradient-to-r from-primary-black/40 to-primary-black/40"
                url={imgSrc}
              />
            </CarouselItem>
          ),
        )}
      </CarouselContent>

      <div className="fixed bottom-0 left-1/2 flex w-full translate-x-[-50%] flex-col items-center px-6 md:absolute md:px-8">
        <ProgressDots
          className="mb-10"
          current={currentSlide}
          dots={slides.length}
          onProgressDotClick={onProgressDotClick}
        />
        {after}
      </div>
    </Carousel>
  );
};
