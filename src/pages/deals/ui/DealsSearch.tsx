import { Button } from '@components/ui/button';
import { SearchInput } from '@components/ui/search';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { useIsMobileView } from '@hooks/system';
import { getRouteApi } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { mobileSearchModel } from '@widgets/mobile-search/model';
import { debounce } from 'lodash';
import { ArrowLeft } from 'lucide-react';
import { Suspense, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import type { NullableDealCategoryValue } from '../types';
import { DealsCategoriesSelectDesktop } from './DealsCategoriesSelectDesktop';

const routeApi = getRouteApi('/_protected/_main/deals');

const dealsIdRootPathname = `${ROUTE_NAMES.deals}/`;

export const DealsSearch = ({ className }: { className?: string }) => {
  const navigate = routeApi.useNavigate();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const isMobileView = useIsMobileView();
  const { title, category } = routeApi.useSearch();

  const currentPath = window.location.pathname;
  const isDealPage =
    currentPath.includes(dealsIdRootPathname) &&
    currentPath.split(dealsIdRootPathname)[1];
  const dealId = isDealPage
    ? currentPath.split(dealsIdRootPathname)[1]
    : undefined;

  const [searchQuery, setSearchQuery] = useState(title ?? '');

  useEffect(() => {
    if (!title) {
      setSearchQuery('');
    }
  }, [title]);

  const debouncedOnDealSearch = useRef(
    debounce((title: string) => {
      navigate({
        resetScroll: false,
        replace: true,
        search: title
          ? {
              title: title,
            }
          : undefined,
      });
    }, 300),
  ).current;

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    debouncedOnDealSearch(value);
  };

  const onCategorySearch = (category: NullableDealCategoryValue) => {
    setSearchQuery('');
    navigate({
      resetScroll: false,
      replace: true,
      search: {
        category: category || undefined,
      },
    });
  };

  const handleCategoryChange = (category: NullableDealCategoryValue) => {
    onCategorySearch(category);
  };

  const onSearchClear = () => {
    handleSearchChange('');
    navigate({
      resetScroll: false,
      replace: true,
      search: undefined,
    });
  };

  const handleOnSearchClick = () => {
    if (isMobileView) {
      navigate({
        to: ROUTE_NAMES.deals,
      });
      onCategorySearch(undefined);
      mobileSearchModel.openSearch();
    }
    return;
  };

  const handleOnReturn = () => {
    if (dealId) {
      navigate({
        to: ROUTE_NAMES.deals,
        replace: true,
        search: {
          category,
        },
      });
      return;
    }

    navigate({
      replace: true,
      search: undefined,
    });
  };

  return (
    <div className={cn(className, 'flex w-full flex-row gap-4')}>
      <div className="flex w-full flex-row items-center gap-2">
        {isMobileView && (category || dealId) && (
          <div className="-translate-x-[60px] animate-slide-in-left opacity-0">
            <Button
              variant="white"
              size="small"
              onClick={handleOnReturn}
              className="rounded-full bg-white"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </div>
        )}

        <SearchInput
          value={searchQuery}
          onChange={handleSearchChange}
          onClick={handleOnSearchClick}
          className={'w-full bg-neutral-10'}
          placeholder={t(LOCIZE_DEALS_KEYS.dealsSearch)}
          onClear={!searchQuery ? undefined : onSearchClear}
        />
      </div>

      {!isMobileView && (
        <Suspense
          fallback={
            <Skeleton className=" h-[3.125rem] w-[10rem] rounded-3xl" />
          }
        >
          <DealsCategoriesSelectDesktop
            value={category}
            onSelect={handleCategoryChange}
          />
        </Suspense>
      )}
    </div>
  );
};
