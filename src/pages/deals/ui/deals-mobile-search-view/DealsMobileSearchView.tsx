import { Skeleton } from '@components/ui/skeleton';
import { getRouteApi } from '@tanstack/react-router';
import { Suspense } from 'react';

import { DealsCategorySection } from '../deals-feature-section/DealsCategoriesSection';
import { CategoryList } from './CategoryList';

const routeApi = getRouteApi('/_protected/_main/deals');

export const DealsMobileSearchView = () => {
  const { title, category } = routeApi.useSearch();

  const isCategoryListVisible = !(category || title);

  const isDealsFeatureSectionVisible = category || title;

  return (
    <div className="no-scrollbar mt-[2.5rem] flex h-full flex-col gap-4 overflow-y-auto px-6 pb-[10rem]">
      {isCategoryListVisible && (
        <Suspense
          fallback={<Skeleton className=" h-full w-full rounded-2xl" />}
        >
          <div className="translate-y-5 animate-fade-in-up opacity-0">
            <CategoryList />
          </div>
        </Suspense>
      )}

      {isDealsFeatureSectionVisible && (
        <Suspense
          fallback={<Skeleton className=" h-full w-full rounded-2xl" />}
        >
          <div className="translate-y-5 animate-fade-in-up opacity-0">
            <DealsCategorySection className="px-0" />
          </div>
        </Suspense>
      )}
    </div>
  );
};
