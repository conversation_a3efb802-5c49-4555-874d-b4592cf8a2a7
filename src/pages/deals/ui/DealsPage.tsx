import { MobileSearch } from '@components/mobile-search/ui/MobileSearch';
import type { SeoData } from '@components/SeoHelmet';
import { SeoHelmet } from '@components/SeoHelmet';
import { Typography } from '@components/typography';
import { Dialog } from '@components/ui/dialog';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useIsMobileView } from '@hooks/system/useIsMobileView';
import { getRouteApi } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { DealsCarousel } from '@widgets/deals/carousel/ui/DealsCarousel';
import { mobileSearchModel } from '@widgets/mobile-search/model';
import { useUnit } from 'effector-react';
import { debounce } from 'lodash';
import { lazy, Suspense, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DealCategoryBreadcrumb } from './DealCategoryBreadcrumb';
import styles from './Deals.module.css';
import { DealsCategorySection } from './deals-feature-section/DealsCategoriesSection';
import DealMainSection from './deals-main-section/DealMainSection';
import { DealsMobileSearchView } from './deals-mobile-search-view/DealsMobileSearchView';
import { DealsCategoriesSelectMobile } from './DealsCategoriesSelectMobile';
import { DealsSearch } from './DealsSearch';

const routeApi = getRouteApi('/_protected/_main/deals');

const SubscribeNewsletterPopup = lazy(() =>
  import('@widgets/subscribe-newsletter/popup').then(
    ({ SubscribeNewsletterPopup }) => ({
      default: SubscribeNewsletterPopup,
    }),
  ),
);

export const Deals = () => {
  const isMobileView = useIsMobileView();
  const navigate = routeApi.useNavigate();
  const { showNewsletterPopup, category, title } = routeApi.useSearch();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const isDealsMainView = !title && !category;

  const seoData: SeoData = useMemo(() => {
    if (category) {
      return null;
    }

    return {
      title: t(LOCIZE_DEALS_KEYS.meta.title),
      description: t(LOCIZE_DEALS_KEYS.meta.description),
      keywords: t(LOCIZE_DEALS_KEYS.meta.keywords),
    };
  }, [category, t]);

  return (
    <>
      <SeoHelmet seoData={seoData} />
      {isMobileView ? <DealsSearch className="px-6 py-4 md:px-12" /> : null}

      <DealsCarousel className={cn('px-0 md:p-12', styles.dealsCarousel)} />

      <div className={cn(styles.dealsContainer, styles.dealsTitle)}>
        <Typography variant={isMobileView ? 'l' : 'xl'}>
          {t(LOCIZE_DEALS_KEYS.title)}
        </Typography>
        <Typography className={styles.dealsTitleDescription}>
          {t(LOCIZE_DEALS_KEYS.description)}
        </Typography>
      </div>

      <div className={styles.deals}>
        {!isMobileView ? <DealsSearch className="px-6 md:px-12" /> : null}

        {isMobileView && !category && (
          <Suspense
            fallback={<Skeleton className="h-[5.4rem] w-full rounded-2xl" />}
          >
            <DealsCategoriesSelectMobile />
          </Suspense>
        )}

        <Suspense
          fallback={
            <Skeleton className="mx-6 h-[20rem] rounded-2xl md:mx-12" />
          }
        >
          {!isMobileView && !isDealsMainView && (
            <DealCategoryBreadcrumb className="px-6 md:px-12" />
          )}

          <div
            data-category={category}
            className="translate-y-5 animate-fade-in-up opacity-0"
          >
            {isDealsMainView ? (
              <DealMainSection />
            ) : (
              <DealsCategorySection className="px-6 md:px-12" />
            )}
          </div>
        </Suspense>
      </div>

      <Dialog
        open={showNewsletterPopup}
        onOpenChange={(state) => {
          if (!state)
            navigate({
              replace: true,
            });
        }}
      >
        <Suspense>
          {showNewsletterPopup ? (
            <SubscribeNewsletterPopup shouldNavigateToDashboard={false} />
          ) : null}
        </Suspense>
      </Dialog>
    </>
  );
};

export const DealsPage = () => {
  const isSearchActive = useUnit(mobileSearchModel.$isSearchActive);
  const isMobileView = useIsMobileView();
  const navigate = routeApi.useNavigate();

  const { title, category } = routeApi.useSearch();

  const isSearchQueryPresent = category || title;

  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const [searchQuery, setSearchQuery] = useState('');

  const debouncedOnDealSearch = useRef(
    debounce((title: string) => {
      navigate({
        replace: true,
        search: title
          ? {
              title: title,
            }
          : undefined,
      });
    }, 300),
  ).current;

  const handleSearchChange = (value: string) => {
    setSearchQuery(value);
    debouncedOnDealSearch(value);
  };

  const onClear = () => {
    handleSearchChange('');

    navigate({
      replace: true,
      search: undefined,
    });
  };

  const onCloseSearch = () => {
    if (isSearchQueryPresent) {
      setSearchQuery('');
      navigate({
        replace: true,
        search: undefined,
      });

      return;
    }

    mobileSearchModel.closeSearch();
  };

  return (
    <>
      {isSearchActive && isMobileView ? (
        <MobileSearch
          value={searchQuery}
          onChange={handleSearchChange}
          placeholder={t(LOCIZE_DEALS_KEYS.dealsSearch)}
          onClear={onClear}
          onCloseSearch={onCloseSearch}
        >
          <DealsMobileSearchView />
        </MobileSearch>
      ) : (
        <Deals />
      )}
    </>
  );
};
