import { Image } from '@components/image';
import { Typography } from '@components/typography';
import { Badge } from '@components/ui/badge';
import { APP_COUNTRY } from '@config/envs';
import { ROUTE_NAMES } from '@config/routes';
import { useRudderStackAnalytics } from '@entities/analytics';
import { DEALS_RUDDERSTACK_EVENTS } from '@entities/deals/config';
import { Link } from '@tanstack/react-router';

import { isDealCategory } from '../config';
import type { Deal } from '../types';

export const DealCard = ({ deal }: { deal: Deal }) => {
  const rudderStackAnalytics = useRudderStackAnalytics();

  const onDealCardClick = () => {
    rudderStackAnalytics.trackEvent({
      event: DEALS_RUDDERSTACK_EVENTS.CUSTOMER_PROFILE_ESTO_DEAL_LINK_CLICKED,
      properties: {
        country: APP_COUNTRY,
        brandId: deal.id ?? '',
        brand: deal.merchantName ?? '',
      },
    });
  };

  const categoryName =
    deal.categoryName && isDealCategory(deal.categoryName)
      ? deal.categoryName
      : undefined;

  return (
    <Link
      onClick={onDealCardClick}
      to={`${ROUTE_NAMES.deals}/${deal.id}`}
      search={{ category: categoryName }}
      key={deal.id}
      className="group cursor-pointer overflow-hidden bg-white"
    >
      <div className="overflow-hidden rounded-lg">
        <div className="relative">
          <Image
            src={deal.imagePath ?? ''}
            alt={deal.title ?? ''}
            className="aspect-square w-full object-cover transition-transform group-hover:scale-105"
          />

          {deal?.discountLabel && (
            <Badge
              variant="discount"
              className="pointer-events-none absolute bottom-0 left-0 h-12 rounded-none rounded-tr-lg"
            >
              <Typography variant="text-l" affects="semibold">
                {deal?.discountLabel}
              </Typography>
            </Badge>
          )}
        </div>
      </div>

      <div className="mt-3 flex-col">
        <div className="flex items-center gap-2">
          {deal.merchantLogoPath && (
            <Image
              src={deal.merchantLogoPath}
              alt="merchant-logo"
              className="h-7 w-7 rounded-lg bg-white [&>img]:object-contain"
            />
          )}
          <Typography className="text-neutral-500" variant="text-s">
            {deal.merchantName}
          </Typography>
        </div>

        <Typography className="mt-2" affects="medium">
          {deal.title}
        </Typography>
      </div>
    </Link>
  );
};

export default DealCard;
