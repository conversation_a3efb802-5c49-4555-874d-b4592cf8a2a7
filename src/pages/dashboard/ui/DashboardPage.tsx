import { Helmet } from '@components/Helmet';
import { Dialog } from '@components/ui/dialog';
import { LOCIZE_DASHBOARD_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { CreditLineCardSkeleton } from '@entities/credit-line';
import { useFeatureToggles } from '@hooks/system';
import { getRouteApi } from '@tanstack/react-router';
import {
  ActiveAgreementsList,
  ActiveAgreementsSkeleton,
} from '@widgets/active-agreements';
import { LoanOffers, LoanOffersSkeleton } from '@widgets/loan-offers';
import { lazy, Suspense, useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { useIsSubscribeNewslettersPopupShown } from '../hooks/useIsSubscribeNewslettersPopupShown';
import { DashboardCreditLineBalance } from './dashboard-credit-line-balance';
import {
  DashboardSubscriptionsCarousel,
  DashboardSubscriptionsCarouselSkeleton,
} from './dashboard-subscriptions-carousel';
import styles from './DashboardPage.module.css';

const SubscribeNewsletterPopup = lazy(() =>
  import('@widgets/subscribe-newsletter/popup').then((module) => ({
    default: module.SubscribeNewsletterPopup,
  })),
);

const routeApi = getRouteApi('/_protected/_main/dashboard');

export const DashboardPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.dashboard);
  const { agreementsFeature } = useFeatureToggles();

  const navigate = routeApi.useNavigate();

  const { isSubscribeNewslettersPopupShown, closeNewslettersPopup } =
    useIsSubscribeNewslettersPopupShown();

  const handleDialogClose = useCallback(
    (open: boolean) => {
      if (open) {
        return;
      }

      if (isSubscribeNewslettersPopupShown) {
        closeNewslettersPopup();
      }

      navigate({
        replace: true,
        resetScroll: false,
      });
    },
    [navigate, isSubscribeNewslettersPopupShown, closeNewslettersPopup],
  );

  return (
    <>
      <Helmet title={t(LOCIZE_DASHBOARD_KEYS.pageTitle)} />
      <div className={styles.page}>
        <Suspense fallback={<CreditLineCardSkeleton />}>
          <DashboardCreditLineBalance />
        </Suspense>

        <div className={styles.container}>
          <Suspense fallback={<DashboardSubscriptionsCarouselSkeleton />}>
            <DashboardSubscriptionsCarousel />
          </Suspense>

          <Suspense fallback={<LoanOffersSkeleton />}>
            <LoanOffers />
          </Suspense>

          {agreementsFeature ? (
            <Suspense fallback={<ActiveAgreementsSkeleton />}>
              <ActiveAgreementsList />
            </Suspense>
          ) : null}

          <Dialog
            open={isSubscribeNewslettersPopupShown}
            onOpenChange={handleDialogClose}
          >
            <Suspense>
              {isSubscribeNewslettersPopupShown && <SubscribeNewsletterPopup />}
            </Suspense>
          </Dialog>
        </div>
      </div>
    </>
  );
};
