import { Typography } from '@components/typography';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { Skeleton } from '@components/ui/skeleton';
import { useGetDashboardSubscriptionsCarouselBanners } from '@pages/dashboard/hooks/useGetDashboardSubscriptionsCarouselBanners';
import Autoplay from 'embla-carousel-autoplay';
import Fade from 'embla-carousel-fade';
import { useState } from 'react';

import styles from './DashboardSubscriptionsCarousel.module.css';
import { DashboardSubscriptionsCarouselButton } from './DashboardSubscriptionsCarouselButton';

export const DashboardSubscriptionsCarousel = () => {
  const [api, setApi] = useState<CarouselApi>();

  const banners = useGetDashboardSubscriptionsCarouselBanners();

  if (banners.length === 0) return null;

  return (
    <Carousel
      className={styles.container}
      opts={{
        loop: true,
        duration: 30,
      }}
      setApi={setApi}
      onMouseLeave={() => api?.plugins().autoplay.play()}
      plugins={[
        Autoplay({
          delay: 5000,
          stopOnMouseEnter: true,
          playOnInit: true,
          stopOnInteraction: true,
          // added to fix onMouseEnter and onMouseLeave events
          rootNode: (emblaRoot) => emblaRoot.parentElement,
        }),
        Fade(),
      ]}
    >
      <div className={styles.bannersContainer}>
        <CarouselContent isInteractive>
          {banners.map((banner) => (
            <CarouselItem key={banner.id}>
              <div
                className={styles.banner}
                key={banner.id}
                style={{
                  backgroundImage: banner.img
                    ? `url(${banner.img})`
                    : undefined,
                }}
              >
                <div className={styles.content}>
                  <Typography variant="xxs" className={styles.text}>
                    {banner.title}
                  </Typography>
                  <Typography
                    variant="text-s"
                    affects="semibold"
                    className={styles.text}
                  >
                    {banner.description}
                  </Typography>

                  <DashboardSubscriptionsCarouselButton id={banner.id} />
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
        {banners.length > 1 && (
          <>
            <CarouselPrevious className={styles.arrowPrevious} />
            <CarouselNext className={styles.arrowNext} />
          </>
        )}
      </div>
    </Carousel>
  );
};

export const DashboardSubscriptionsCarouselSkeleton = () => (
  <Skeleton className={styles.skeleton} />
);
