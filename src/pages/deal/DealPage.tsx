import { Image } from '@components/image';
import type { SeoData } from '@components/SeoHelmet';
import { SeoHelmet } from '@components/SeoHelmet';
import { Typography } from '@components/typography';
import { Badge } from '@components/ui/badge';
import { Button } from '@components/ui/button';
import { Skeleton } from '@components/ui/skeleton';
import { APP_COUNTRY } from '@config/envs';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useRudderStackAnalytics } from '@entities/analytics';
import { DEALS_RUDDERSTACK_EVENTS } from '@entities/deals/config';
import { useGetDeal } from '@entities/deals/hooks/useGetDeal';
import { useIsMobileView, useToast } from '@hooks/system';
import { DealsSearch } from '@pages/deals/ui/DealsSearch';
import RelatedDeals from '@pages/deals/ui/RelatedDeals';
import { isCategoryWithSeoData } from '@pages/deals/utils';
import { getRouteApi } from '@tanstack/react-router';
import { Copy } from 'lucide-react';
import { Suspense, useEffect, useMemo, useState } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { useCopyToClipboard } from 'react-use';

import { DealBreadcrumb } from './DealBreadcrumb';

const routeApi = getRouteApi('/_protected/_main/deals/$dealId');

export const DealPage = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);
  const { dealId } = routeApi.useParams();
  const { data: deal } = useGetDeal({ dealId });

  const isMobileView = useIsMobileView();
  const rudderStackAnalytics = useRudderStackAnalytics();
  const [{ error }, copyToClipboard] = useCopyToClipboard();
  const { showErrorMessage } = useToast();

  const [isCopying, setIsCopying] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  const onCopyButtonClick = () => {
    if (isCopying || !deal?.promocode || !deal?.merchantName) {
      return;
    }

    setIsCopying(true);

    copyToClipboard(deal.promocode);

    rudderStackAnalytics.trackEvent({
      event:
        DEALS_RUDDERSTACK_EVENTS.CUSTOMER_PROFILE_ESTO_DEAL_PROMOCODE_COPIED,
      properties: {
        country: APP_COUNTRY,
        brandId: dealId,
        brand: deal.merchantName,
        code: deal.promocode,
      },
    });

    setTimeout(() => {
      setIsCopying(false);
    }, 1500);
  };

  const categoryName = deal?.categoryName;

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  useEffect(() => {
    const scrollContainer = document.getElementById(
      'main-layout-scroll-container',
    );
    if (scrollContainer) {
      scrollContainer.scrollTo({ top: 0 });
    } else {
      // Fallback to window scroll if container not found
      window.scrollTo({ top: 0 });
    }
  }, [dealId]);

  const onRedirectButtonClick = () => {
    if (!deal?.merchantName || !deal?.destinationUrl) {
      return;
    }

    setIsRedirecting(true);

    rudderStackAnalytics.trackEvent({
      event: DEALS_RUDDERSTACK_EVENTS.CUSTOMER_PROFILE_ESTO_DEAL_LINK_CLICKED,
      properties: {
        country: APP_COUNTRY,
        brandId: dealId,
        brand: deal.merchantName,
        to_url: deal.destinationUrl,
      },
    });

    window.open(deal.destinationUrl, '_blank');

    setTimeout(() => {
      setIsRedirecting(false);
    }, 1500);
  };

  useEffect(() => {
    if (error) showErrorMessage(error.message);
  }, [error, showErrorMessage]);

  const seoData: SeoData = useMemo(() => {
    if (!dealId || !isCategoryWithSeoData(deal?.categoryName)) {
      return null;
    }

    const categoryData = LOCIZE_DEALS_KEYS.category[deal.categoryName];
    const title = deal?.title ?? '';

    return {
      title: t(title),
      description: t(categoryData.meta.description),
      keywords: t(categoryData.meta.keywords),
    };
  }, [dealId, t]);

  return (
    <div className="translate-y-5 animate-fade-in-up opacity-0">
      <SeoHelmet seoData={seoData} />

      <div className="">
        {isMobileView ? (
          <DealsSearch className="p-4 px-6 md:pt-12" />
        ) : (
          <DealBreadcrumb
            category={deal?.categoryName}
            title={deal?.title}
            className="px-6 md:px-12 md:pt-12 md:pb-10"
          />
        )}

        <div className="mb-[4.5rem] grid max-w-6xl gap-8 px-6 md:grid-cols-2 md:px-12">
          {/* Deal Image */}
          <Image
            src={deal?.imageUrl ?? ''}
            alt="deal-pic"
            className="h-auto w-full rounded-lg"
          />

          {/* Deal Details */}
          <div>
            {deal?.discountLabel && (
              <Badge
                variant="discount"
                className="pointer-events-none h-12 rounded-none rounded-tr-lg"
              >
                <Typography variant="text-l" affects="semibold">
                  {deal?.discountLabel}
                </Typography>
              </Badge>
            )}

            <Typography className="mt-4" variant={isMobileView ? 's' : 'm'}>
              {deal?.title}
            </Typography>

            <Image
              src={deal?.merchantLogo ?? ''}
              alt="merchant-logo"
              className="mt-4 h-[3.5rem] w-[3.5rem] [&>img]:object-contain"
            />

            <Typography className="mt-6">{deal?.description}</Typography>

            <Typography className="mt-6">
              <Trans
                i18nKey={LOCIZE_DEALS_KEYS.deal.campaignEndTimeDisclaimer}
                t={t}
                values={{ endDate: deal?.endTime }}
              />
              <br />
              <Trans
                i18nKey={LOCIZE_DEALS_KEYS.deal.discountCodeDisclaimer}
                t={t}
                values={{ discount: deal?.discountLabel }}
              />
              <br />
              {t(LOCIZE_DEALS_KEYS.deal.orderConvenienceDisclaimer)}
            </Typography>

            {deal?.promocode && (
              <Button
                variant="grey"
                className="mt-10"
                fullWidth
                onClick={onCopyButtonClick}
              >
                {isCopying ? (
                  t(LOCIZE_DEALS_KEYS.dialogCopyButtonSuccess)
                ) : (
                  <>
                    <Copy />
                    {deal?.promocode}
                  </>
                )}
              </Button>
            )}

            <Button
              className="mt-2"
              variant="yellow2"
              fullWidth
              loading={isRedirecting}
              onClick={onRedirectButtonClick}
            >
              {t(LOCIZE_DEALS_KEYS.deal.ctaButtonLabel)}
            </Button>
          </div>
        </div>
        <Suspense
          fallback={<Skeleton className="h-[24rem] rounded-2xl md:mx-12" />}
        >
          <RelatedDeals category={categoryName} dealId={dealId} />
        </Suspense>
      </div>
    </div>
  );
};
