import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { PURCHASE_FLOW_ROUTE_NAME } from '@config/routes';
import {
  MarketingSearchParam,
  MarketingSearchParamValue,
} from '@config/search-params';
import { PARTNER_SHOPS_LINKS } from '@entities/homepage/config';
import { useExampleCreditLimitPricingConfig } from '@entities/pricing/hooks/useExampleCreditLimitPricingConfig';
import { useIsUserAuthorized, useUserCreditAccount } from '@entities/user';
import { getRouteApi } from '@tanstack/react-router';
import { addParamToUrlString } from '@utils/url';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { DEALS_CAROUSEL_SLIDES_IDS } from './config';

type DealsCarouselSlideId =
  (typeof DEALS_CAROUSEL_SLIDES_IDS)[keyof typeof DEALS_CAROUSEL_SLIDES_IDS];
type DealsCarouselSlide = {
  id: DealsCarouselSlideId;
  imgSrc: string;
  title: string;
  ctaLabel: string;
  onCtaClick: () => void;
};

const routeApi = getRouteApi('/_protected/_main/deals');

export const useDealsCarouselSlides = () => {
  const { t, i18n } = useTranslation(LOCIZE_NAMESPACES.deals);
  const { data: creditAccount } = useUserCreditAccount();
  const { data: isAuthorized } = useIsUserAuthorized();
  const { exampleDefaultCreditLimitAmount } =
    useExampleCreditLimitPricingConfig();

  const navigate = routeApi.useNavigate();

  return useMemo(() => {
    const slides: DealsCarouselSlide[] = [
      {
        id: DEALS_CAROUSEL_SLIDES_IDS.NEWSLETTER,
        imgSrc: '/images/deals/carousel.slide-2.webp',
        title: t(LOCIZE_DEALS_KEYS.carousel.slide2.title),
        ctaLabel: t(LOCIZE_DEALS_KEYS.carousel.slide2.ctaLabel),
        onCtaClick: () => {
          navigate({
            replace: true,
            search: {
              showNewsletterPopup: true,
            },
          });
        },
      },
      {
        id: DEALS_CAROUSEL_SLIDES_IDS.PARTNER_SHOP,
        imgSrc: '/images/deals/carousel.slide-3.webp',
        title: t(LOCIZE_DEALS_KEYS.carousel.slide3.title),
        ctaLabel: t(LOCIZE_DEALS_KEYS.carousel.slide3.ctaLabel),
        onCtaClick: () => {
          window.open(PARTNER_SHOPS_LINKS[i18n.language], '_self');
        },
      },
    ];

    if (!creditAccount?.signedAt) {
      slides.unshift({
        id: DEALS_CAROUSEL_SLIDES_IDS.CREDIT_LINE,
        imgSrc: '/images/deals/carousel.slide-1.webp',
        title: t(LOCIZE_DEALS_KEYS.carousel.slide1.title, {
          amount: isAuthorized
            ? creditAccount?.creditLimit
            : exampleDefaultCreditLimitAmount,
        }),
        ctaLabel: t(LOCIZE_DEALS_KEYS.carousel.slide1.ctaLabel),
        onCtaClick: () => {
          window.open(
            addParamToUrlString({
              url: PURCHASE_FLOW_ROUTE_NAME.creditLineInterestFree,
              param: MarketingSearchParam.UTM_SOURCE,
              value: MarketingSearchParamValue.UTM_SOURCE_CUSTOMER_PROFILE,
            }),
            '_self',
          );
        },
      });
    }

    return slides;
  }, [
    t,
    i18n.language,
    navigate,
    creditAccount?.creditLimit,
    exampleDefaultCreditLimitAmount,
    isAuthorized,
    creditAccount?.signedAt,
  ]);
};
