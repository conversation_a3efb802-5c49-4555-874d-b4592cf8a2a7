import type { AppLanguage } from '@/shared/types';

export type HomepageHeaderExpandableLinkBase = {
  href?: string | Record<AppLanguage, string>;
  title: string;
  subtitle?: string;
};

export type HomepageHeaderLinkProps = {
  title: string;
  href?: string | Record<AppLanguage, string>;
  isOpenInNewTab?: boolean;
  className?: string;
  isActive?: boolean;
  isExpandable?: boolean;
  menuItems?: (HomepageHeaderExpandableLinkBase & {
    isOpenInNewTab?: boolean;
  })[];
};
