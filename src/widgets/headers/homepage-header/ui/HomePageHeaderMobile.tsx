import { Button } from '@components/ui/button';
import { Separator } from '@components/ui/separator';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { homepageMobileMenuModel } from '@entities/homepage/model';
import { HomePageBurgerButton } from '@features/homepage-burger-button';
import { LanguageRowSelector } from '@features/language-selector/ui/LanguageRowSelector';
import { useNavigate } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { useUnit } from 'effector-react';
import { useTranslation } from 'react-i18next';

import Logo from '@/shared/assets/logo-desktop.svg?react';

import {
  HOMEPAGE_MOBILE_BOTTOM_HEADER_LINKS,
  HOMEPAGE_MOBILE_TOP_HEADER_LINKS,
} from '../config';
import { HomepageHeaderLink } from './homepage-header-link';
import styles from './HomepageHeader.module.css';

export const HomePageHeaderMobile = () => {
  const isOpen = useUnit(homepageMobileMenuModel.store.$isOpen);

  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);
  const { t: tN } = useTranslation(LOCIZE_NAMESPACES.navigation);

  const navigate = useNavigate();

  const onCtaClick = () => {
    navigate({
      to: ROUTE_NAMES.auth,
      search: {
        authMethod: APP_CONFIG.authMethods[0],
        redirectUrl: location.pathname,
      },
    });
  };

  return (
    <div className={styles.homepageHeaderMobile}>
      <div className={styles.homepageHeaderMobileMenuContainer}>
        <Logo />
        <HomePageBurgerButton />
      </div>

      <Separator decorative className={styles.separator} />

      <aside
        className={cn(
          styles.homepageHeaderMobileMenu,
          isOpen && styles.homepageHeaderMobileMenuIsOpen,
        )}
      >
        <div className={styles.homepageHeaderMobileMenuAuth}>
          <Button variant="grey" onClick={onCtaClick}>
            {t(LOCIZE_AUTH_KEYS.logIn)}
          </Button>
          <Button onClick={onCtaClick}>{t(LOCIZE_AUTH_KEYS.register)}</Button>
        </div>

        <Separator decorative className={styles.separator} />

        <div className={styles.homepageHeaderMobileMenuLinks}>
          {HOMEPAGE_MOBILE_TOP_HEADER_LINKS.map(({ title, ...link }) => (
            <HomepageHeaderLink
              key={title}
              affects="bold"
              variant="text-l"
              className={cn(styles.homepageHeaderMobileMenuLink)}
              title={tN(title)}
              t={tN}
              {...link}
            />
          ))}
        </div>

        <Separator decorative className={styles.separator} />

        <div className={styles.homepageHeaderMobileMenuLinks}>
          {HOMEPAGE_MOBILE_BOTTOM_HEADER_LINKS.map(({ title, ...link }) => (
            <HomepageHeaderLink
              key={title}
              affects="bold"
              variant="text-l"
              className={cn(styles.homepageHeaderMobileMenuLink)}
              title={tN(title)}
              t={tN}
              {...link}
            />
          ))}
        </div>

        <LanguageRowSelector className={styles.languageRowSelector} />
      </aside>
    </div>
  );
};
