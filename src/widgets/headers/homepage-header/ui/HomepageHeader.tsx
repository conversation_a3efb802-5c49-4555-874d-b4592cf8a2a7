import { useIsMobileView } from '@hooks/system';

import styles from './HomepageHeader.module.css';
import { HomePageHeaderDesktop } from './HomePageHeaderDesktop';
import { HomePageHeaderMobile } from './HomePageHeaderMobile';

export const HomepageHeader = () => {
  const isMobileView = useIsMobileView();

  if (isMobileView) {
    return <HomePageHeaderMobile />;
  }

  return (
    <header className={styles.homepageHeader}>
      <HomePageHeaderDesktop />
    </header>
  );
};
