import { DefaultLink } from '@components/DefaultLink';
import { Typography } from '@components/typography';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@components/ui/navigation-menu';
import { cn } from '@utils/tailwind';
import type { TFunction } from 'i18next';

import styles from './ExpandableMenuItem.module.css';

type ExpandableItemBase = {
  href: string;
  title: string;
  subtitle?: string;
};

type ExpandableMenuItemProps<T extends ExpandableItemBase> = {
  title: string;
  menuItems?: T[];
  className?: string;
  t?: TFunction;
  isOpenInNewTab?: boolean;
};

export const ExpandableMenuItem = <T extends ExpandableItemBase>({
  title,
  menuItems,
  className,
  isOpenInNewTab = false,
  t,
}: ExpandableMenuItemProps<T>) => {
  return (
    <div className={cn(styles.container, className)}>
      <NavigationMenu>
        <NavigationMenuList>
          <NavigationMenuItem>
            <NavigationMenuTrigger>
              <Typography className={styles.menuTriggerTitle}>
                {title}
              </Typography>
            </NavigationMenuTrigger>
            <NavigationMenuContent>
              <div className={styles.menuContent}>
                {menuItems?.map(({ href, title, subtitle }) => (
                  <NavigationMenuLink key={href} asChild>
                    <DefaultLink
                      href={href}
                      className={cn(styles.menuLink)}
                      target={isOpenInNewTab ? '_blank' : undefined}
                    >
                      <div className={styles.menuTitle}>
                        {t ? t(title) : title}
                      </div>
                      {subtitle && (
                        <div className={styles.menuSubtitle}>
                          {t ? t(subtitle) : subtitle}
                        </div>
                      )}
                    </DefaultLink>
                  </NavigationMenuLink>
                ))}
              </div>
            </NavigationMenuContent>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>
    </div>
  );
};
