.container {
  @apply flex justify-center;
}

.menuContent {
  @apply grid w-[400px] gap-1 p-4;
}

.menuLink {
  @apply grid h-auto w-full items-center justify-start gap-1 rounded-md bg-background p-4 transition-colors hover:bg-accent hover:text-accent-foreground disabled:pointer-events-none disabled:opacity-50;
}

.menuTriggerTitle {
  @apply font-normal text-inherit;
}

.menuTitle {
  @apply text-sm font-medium leading-none group-hover:underline;
}

.menuSubtitle {
  @apply line-clamp-2 text-sm leading-snug text-muted-foreground;
}
