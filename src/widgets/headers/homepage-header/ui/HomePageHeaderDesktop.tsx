import { Button } from '@components/ui/button';
import { APP_CONFIG } from '@config/app';
import { LOCIZE_AUTH_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { ROUTE_NAMES } from '@config/routes';
import { LanguageSelector } from '@features/language-selector';
import { useNavigate } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { useTranslation } from 'react-i18next';

import Logo from '@/shared/assets/logo-desktop.svg?react';

import {
  HOMEPAGE_DESKTOP_BOTTOM_HEADER_LINKS,
  HOMEPAGE_DESKTOP_TOP_HEADER_LINKS,
} from '../config';
import { HomepageHeaderLink } from './homepage-header-link';
import styles from './HomepageHeader.module.css';

export const HomePageHeaderDesktop = () => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.auth);
  const { t: tN } = useTranslation(LOCIZE_NAMESPACES.navigation);
  const navigate = useNavigate();

  const onCtaClick = () => {
    navigate({
      to: ROUTE_NAMES.auth,
      search: {
        authMethod: APP_CONFIG.authMethods[0],
        redirectUrl: location.pathname,
      },
    });
  };

  return (
    <>
      <div className={styles.headerTop}>
        <div className={styles.headerTopContainer}>
          <div className={styles.headerTopLeft}>
            {HOMEPAGE_DESKTOP_TOP_HEADER_LINKS.map(
              ({ title, isActive, ...link }) => (
                <HomepageHeaderLink
                  key={title}
                  className={cn(
                    styles.headerTopLeftLink,
                    isActive && styles.activeLinkPrimary,
                  )}
                  title={tN(title)}
                  affects="medium"
                  {...link}
                />
              ),
            )}
          </div>

          <LanguageSelector className={styles.languageSelector} />
        </div>
      </div>

      <div className={styles.headerBottom}>
        <div className={styles.headerBottomContainer}>
          <Logo className={styles.logo} />

          <div className={styles.headerBottomCenter}>
            {HOMEPAGE_DESKTOP_BOTTOM_HEADER_LINKS.map(
              ({ title, isActive, ...link }) => (
                <HomepageHeaderLink
                  key={title}
                  className={cn(
                    styles.headerBottomLink,
                    isActive && styles.activeLinkSecondary,
                  )}
                  affects={isActive ? 'semibold' : 'normal'}
                  title={tN(title)}
                  t={tN}
                  {...link}
                />
              ),
            )}
          </div>

          <div className={styles.headerBottomRight}>
            <Button variant="grey" size="small" onClick={onCtaClick}>
              {t(LOCIZE_AUTH_KEYS.logIn)}
            </Button>
            <Button size="small" onClick={onCtaClick}>
              {t(LOCIZE_AUTH_KEYS.register)}
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};
