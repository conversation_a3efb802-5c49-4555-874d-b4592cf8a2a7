import { Typography } from '@components/typography';
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@components/ui/dropdown-menu';
import { COUNTRY_FLAG_BY_LOCALE } from '@config/common';
import { useLanguages } from '@entities/languages';
import { useUpdateUserLanguage } from '@entities/languages/hooks';
import { sidebarModel } from '@entities/sidebar';
import { LANGUAGE_SELECTOR_TEST_KEYS } from '@features/language-selector/config';
import { useIsMobileView } from '@hooks/system';
import { useUnit } from 'effector-react';
import { ChevronDownIcon } from 'lucide-react';
import { type FC, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useToggle } from 'react-use';

import { cn } from '@/shared/utils/tailwind';

export const MainLayoutDesktopLanguageSelector: FC = () => {
  const { i18n } = useTranslation();
  const isMobileView = useIsMobileView();
  const { updateUserLanguage } = useUpdateUserLanguage();
  const [isDropdownMenuOpen, toggleDropdownMenu] = useToggle(false);
  const languages = useLanguages();

  const isOpen = useUnit(sidebarModel.store.$isOpen);

  useEffect(() => {
    if (!isOpen && isMobileView && isDropdownMenuOpen) {
      toggleDropdownMenu(false);
    }
  }, [isMobileView, isDropdownMenuOpen, isOpen]);

  return (
    <DropdownMenu open={isDropdownMenuOpen} onOpenChange={toggleDropdownMenu}>
      <DropdownMenuTrigger className="outline-none" asChild>
        <button
          type="button"
          className={cn(
            'flex items-center gap-3 px-3.5 py-2 hover:bg-stone-50 rounded-lg w-full',
            isDropdownMenuOpen && 'bg-stone-50',
          )}
        >
          <img
            src={COUNTRY_FLAG_BY_LOCALE[i18n.language]}
            alt={i18n.language}
            className="size-5"
          />
          <div className="flex w-full items-center gap-1.5">
            <Typography variant="text-s" className="text-inherit">
              {i18n.language.toUpperCase()}
            </Typography>
            <ChevronDownIcon
              className={cn(
                'size-5 transition-transform',
                isDropdownMenuOpen && 'rotate-180',
              )}
            />
          </div>
        </button>
      </DropdownMenuTrigger>
      {isDropdownMenuOpen && (
        <DropdownMenuContent align="start" className="min-w-[12.5rem]">
          {languages.map((lang) => (
            <DropdownMenuCheckboxItem
              className="cursor-pointer focus:outline-none hover:bg-stone-50 focus:bg-stone-50 [&>span]:right-3.5"
              key={lang}
              arrowPosition="right"
              checked={i18n.language === lang}
              data-testid={`${LANGUAGE_SELECTOR_TEST_KEYS.ITEM}-${lang}`}
              onCheckedChange={() => {
                updateUserLanguage(lang);
                toggleDropdownMenu(false);
              }}
            >
              <div className="flex w-full items-center gap-3 pr-4">
                <img
                  src={COUNTRY_FLAG_BY_LOCALE[lang]}
                  alt={i18n.language}
                  className="size-5"
                />
                <Typography variant="text-s" className="text-inherit">
                  {lang.toUpperCase()}
                </Typography>
              </div>
            </DropdownMenuCheckboxItem>
          ))}
        </DropdownMenuContent>
      )}
    </DropdownMenu>
  );
};
