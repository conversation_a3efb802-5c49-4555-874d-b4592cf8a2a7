.container {
  @apply relative;
}

.content {
  @apply bg-transparent 
  md:px-10
  size-full 
  rounded-3xl 
  grid 
  gap-10
  min-[875px]:grid-cols-[1fr_0.58fr] 
  md:bg-neutral-50;
}

.left {
  @apply py-0
  flex
  flex-col
  items-center
  md:block
  md:py-[2.78125rem];
}

.leftCreditLine {
  @apply md:py-[2.3125rem];
}

.topDisclaimer {
  @apply text-neutral-500;
}

.mobileBanner {
  @apply relative
  bg-white
  block
  max-w-[14.125rem]
  md:hidden;
}

.mobileBannerGreyOverlay {
  transform: translate3d(0, 0, 0);

  @apply absolute
  top-0
  left-0
  w-full
  h-full
  bg-gradient-to-b
  mix-blend-color-burn
  from-[#d3d3d3]
  via-[#d3d3d3] via-[percentage:44.64%]
  to-primary-white;
}

.mobileBannerWhiteOverlay {
  @apply absolute
  top-0
  left-0
  w-full
  h-full
  bg-gradient-to-b
  from-transparent
  via-transparent via-[percentage:44.64%]
  to-primary-white;
}

.mainDisclaimer {
  @apply mt-4
  md:mt-6;
}

.secondaryDisclaimer {
  @apply mt-2;
}

.buttons {
  @apply w-full
  mt-8
  flex
  justify-center
  md:justify-start
  gap-4;
}

.applyButton {
  @apply w-full
  md:w-auto;
}

.right {
  @apply relative
  hidden
  max-w-[56rem]
  min-[875px]:block;
}

.rightContent {
  @apply absolute
  right-0
  bottom-0;
}

.banner {
  @apply max-w-[17.1875rem]
  w-full;
}
