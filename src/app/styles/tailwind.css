@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Our configs */
/* Color design system */
/* Link => https://www.figma.com/file/qRNfEkalc67TUhyFm6AjFf/ESTO-Design-System?type=design&node-id=1-6481&mode=design&t=cv07M8IvBhRx2JAL-0 */

@layer base {
  html,
  body,
  #root {
    @apply h-full font-inter;
  }

  :root {
    /* Primary */
    --color-primary-brand-02: #3300ff;
    --color-primary-black: #000000;
    --color-primary-white: #ffffff;
    /* System */
    --color-system-green-500: #22c55e;
    --color-system-green-100: #dcfce7;
    --color-system-green: #0dea4b;
    --color-system-yellow: #ead40d;
    --color-system-yellow-2: #e2ff09;
    --color-system-yellow-3: #fefce8;
    --color-system-orange: #ea770d;
    --color-system-red: #ea350d;
    /* Neutral */
    --color-neutral-900: #111827;
    --color-neutral-800: #1f2937;
    --color-neutral-700: #374151;
    --color-neutral-600: #4b5563;
    --color-neutral-500: #6b7280;
    --color-neutral-400: #9ca3af;
    --color-neutral-300: #d1d5db;
    --color-neutral-200: #e5e7eb;
    --color-neutral-100: #f3f4f6;
    --color-neutral-50: #f9fafb;
    --color-neutral-10: #f7f7f7;

    /* Stone */
    --color-stone-50: #fafaf9;
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 0.2s;
  }
  .animation-delay-400 {
    animation-delay: 0.4s;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .underline-from-font {
    text-underline-position: from-font;
  }
  .unset {
    position: unset;
  }
}
