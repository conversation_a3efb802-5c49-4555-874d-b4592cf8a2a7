import { Skeleton } from '@components/ui/skeleton';
import { cn } from '@utils/tailwind';
import { useState } from 'react';

type ImageProps = {
  className?: string;
  src: string;
  alt: string;
};

export const Image = ({ className, alt, src }: ImageProps) => {
  const [isLoaded, setLoaded] = useState(false);

  return (
    <div className={cn('overflow-hidden', className)}>
      {!isLoaded && (
        <Skeleton className="size-full rounded-none bg-neutral-300" />
      )}
      <img
        alt={alt}
        src={src}
        className={cn('size-full object-cover', !isLoaded && 'hidden')}
        onLoad={() => setLoaded(true)}
      />
    </div>
  );
};
