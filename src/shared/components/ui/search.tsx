import { Input } from '@components/ui/input';
import { cn } from '@utils/tailwind';
import { Search, X } from 'lucide-react';
import { forwardRef } from 'react';

type SearchInputProps = {
  placeholder: string;
  className?: string;
  onClick?: () => void;
  onClear?: () => void;
  value: string;
  onChange?: (value: string) => void;
};
export interface SearchInputRef {
  clear: () => void;
}

export const SearchInput = forwardRef<SearchInputRef, SearchInputProps>(
  ({ onClick, placeholder, className = '', onClear, value, onChange }) => (
    <div className={`relative rounded-full ${className}`}>
      <Search className="-translate-y-1/2 absolute top-1/2 left-4 h-5 w-5 text-primary-black" />
      <Input
        type="text"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange?.(e.target.value)}
        onClick={() => onClick?.()}
        className={cn(
          'h-12 rounded-full border-0 bg-transparent pl-12 text-base focus-visible:ring-0 focus-visible:ring-offset-0',
          value && 'font-semibold',
        )}
      />
      {onClear && (
        <button
          type="button"
          onClick={() => {
            onClear?.();
          }}
          className="-translate-y-1/2 absolute top-1/2 right-4 rounded-full p-1 text-primary-black hover:bg-neutral-200 hover:text-gray-700 "
          aria-label="Clear search"
        >
          <X className="h-6 w-6" />
        </button>
      )}
    </div>
  ),
);
