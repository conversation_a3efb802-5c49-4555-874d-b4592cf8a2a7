export const LOCIZE_DEALS_KEYS = {
  deals: 'deals',
  dealsAll: 'deals.all',
  dealsSearch: 'deals.search',
  dealsViewAll: 'deals.view-all',
  dealsCategories: 'deals.categories',
  showingResultsFor: 'showing-results-for',
  title: 'title',
  description: 'description',
  dialogCopyButtonSuccess: 'dialog.copy-button.success',
  carousel: {
    slide1: {
      title: 'carousel.slide-1.title',
      ctaLabel: 'carousel.slide-1.cta-label',
    },
    slide2: {
      title: 'carousel.slide-2.title',
      ctaLabel: 'carousel.slide-2.cta-label',
    },
    slide3: {
      title: 'carousel.slide-3.title',
      ctaLabel: 'carousel.slide-3.cta-label',
    },
  },
  deal: {
    campaignEndTimeDisclaimer: 'deal.campaign-end-time-disclaimer',
    discountCodeDisclaimer: 'deal.discount-code-disclaimer',
    orderConvenienceDisclaimer: 'deal.order-convenience-disclaimer',
    copyButtonLabel: 'deal.copy-button-label',
    ctaButtonLabel: 'deal.cta-button-label',
  },

  noResults: 'no-results',
  relatedDeals: 'related-deals',
  featuredDeals: 'featured-deals',
  meta: {
    title: 'page-title',
    description: 'meta.description',
    keywords: 'meta.keywords',
  },
  category: {
    'Home Improvement': {
      name: 'home-improvement',
      meta: {
        title: 'category.home-improvement.meta.title',
        description: 'category.home-improvement.meta.description',
        keywords: 'category.home-improvement.meta.keywords',
      },
    },
    'Bikes and scooters': {
      name: 'bikes-and-scooters',
      meta: {
        title: 'category.bikes-and-scooters.meta.title',
        description: 'category.bikes-and-scooters.meta.description',
        keywords: 'category.bikes-and-scooters.meta.keywords',
      },
    },
    Garden: {
      name: 'garden',
      meta: {
        title: 'category.garden.meta.title',
        description: 'category.garden.meta.description',
        keywords: 'category.garden.meta.keywords',
      },
    },
    'For car owners': {
      name: 'for-car-owners',
      meta: {
        title: 'category.for-car-owners.meta.title',
        description: 'category.for-car-owners.meta.description',
        keywords: 'category.for-car-owners.meta.keywords',
      },
    },
    Kids: {
      name: 'kids',
      meta: {
        title: 'category.kids.meta.title',
        description: 'category.kids.meta.description',
        keywords: 'category.kids.meta.keywords',
      },
    },
    Travel: {
      name: 'travel',
      meta: {
        title: 'category.travel.meta.title',
        description: 'category.travel.meta.description',
        keywords: 'category.travel.meta.keywords',
      },
    },
    Sports: {
      name: 'sport',
      meta: {
        title: 'category.sports.meta.title',
        description: 'category.sports.meta.description',
        keywords: 'category.sports.meta.keywords',
      },
    },
    Pets: {
      name: 'pets',
      meta: {
        title: 'category.pets.meta.title',
        description: 'category.pets.meta.description',
        keywords: 'category.pets.meta.keywords',
      },
    },
    Other: {
      name: 'other',
      meta: {
        title: 'category.other.meta.title',
        description: 'category.other.meta.description',
        keywords: 'category.other.meta.keywords',
      },
    },
    Clothing: {
      name: 'clothing',
      meta: {
        title: 'category.clothing.meta.title',
        description: 'category.clothing.meta.description',
        keywords: 'category.clothing.meta.keywords',
      },
    },
    Jewelry: {
      name: 'jewelry',
      meta: {
        title: 'category.jewelry.meta.title',
        description: 'category.jewelry.meta.description',
        keywords: 'category.jewelry.meta.keywords',
      },
    },
    'Home & Living': {
      name: 'home-and-living',
      meta: {
        title: 'category.home-and-living.meta.title',
        description: 'category.home-and-living.meta.description',
        keywords: 'category.home-and-living.meta.keywords',
      },
    },
    'Health & Wellness': {
      name: 'health-and-wellness',
      meta: {
        title: 'category.health-and-wellness.meta.title',
        description: 'category.health-and-wellness.meta.description',
        keywords: 'category.health-and-wellness.meta.keywords',
      },
    },
    Gifts: {
      name: 'gifts',
      meta: {
        title: 'category.gifts.meta.title',
        description: 'category.gifts.meta.description',
        keywords: 'category.gifts.meta.keywords',
      },
    },
    Furniture: {
      name: 'furniture',
      meta: {
        title: 'category.furniture.meta.title',
        description: 'category.furniture.meta.description',
        keywords: 'category.furniture.meta.keywords',
      },
    },
    Finance: {
      name: 'finance',
      meta: {
        title: 'category.finance.meta.title',
        description: 'category.finance.meta.description',
        keywords: 'category.finance.meta.keywords',
      },
    },
    Electronics: {
      name: 'electronics',
      meta: {
        title: 'category.electronics.meta.title',
        description: 'category.electronics.meta.description',
        keywords: 'category.electronics.meta.keywords',
      },
    },
    Cosmetics: {
      name: 'cosmetics',
      meta: {
        title: 'category.cosmetics.meta.title',
        description: 'category.cosmetics.meta.description',
        keywords: 'category.cosmetics.meta.keywords',
      },
    },
  },
};
