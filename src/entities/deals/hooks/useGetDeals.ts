import { LANGUAGE_ABBREVIATION_BY_SHORTNAME } from '@entities/languages';
import { useTranslation } from 'react-i18next';

import {
  type DealsQueryVariables,
  useSuspenseDealsQuery,
} from '../api/queries.gen';

export const useGetDeals = (variables?: DealsQueryVariables) => {
  const { i18n } = useTranslation();

  return useSuspenseDealsQuery(variables, {
    select: (data) => {
      if (!data?.deals?.data?.length) {
        return null;
      }

      return data.deals.data.map((deal) => {
        const translations = deal?.translations?.find(
          (translation) =>
            translation?.language ===
            LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
        );
        const dealTitle = translations?.deal_title;
        const dealDescription = translations?.description;

        return {
          id: deal?.id,
          title: dealTitle,
          imageUrl: deal?.image_url,
          imagePath: deal?.image_path,
          categoryName: deal?.category_name,
          description: dealDescription,
          merchantName: deal?.merchant?.name,
          merchantLogoPath: deal?.merchant?.logo_path,
          featured: deal?.featured,
          discountLabel: translations?.discount_label,
        };
      });
    },
  });
};
