import { POSTHOG_HOST, POSTHOG_KEY } from '@config/envs';
import { userApi } from '@entities/user';
import type { MeUserFragment } from '@entities/user/api/queries.gen';
import posthog from 'posthog-js';
import { useMount } from 'react-use';

export const isPostHogEnabled = Boolean(POSTHOG_KEY && POSTHOG_HOST);

export const usePosthogAnalytics = () => {
  const { data: user } = userApi.useSuspenseUserQuery(undefined, {
    select: (data) => data?.me ?? null,
  });

  useMount(() => {
    initializePostHog();
    identifyUserInPostHog({ user });
  });
};

const initializePostHog = () => {
  if (!isPostHogEnabled) {
    return;
  }

  posthog.init(POSTHOG_KEY, {
    api_host: POSTHOG_HOST,
    capture_exceptions: true,
    person_profiles: 'always',
    session_idle_timeout_seconds: 180, // 3 minutes
  });
};

type IdentifyUserInPosthogParams = {
  user: Nullable<MeUserFragment>;
};

const identifyUserInPostHog = ({ user }: IdentifyUserInPosthogParams) => {
  if (!user) {
    return;
  }

  posthog.identify(user.id.toString(), {
    userId: user.id || 'No User ID',
    email: user.email || 'No User Email',
    name: !(!user.profile?.first_name && !user.profile?.last_name)
      ? `${user.profile?.first_name ?? ''} ${
          user.profile?.last_name ?? ''
        }`.trimStart()
      : 'No User Name',
  });
};
