import { Typography } from '@components/typography';
import { Badge } from '@components/ui/badge';
import { LOCIZE_COMMON_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useTranslation } from 'react-i18next';

import { cn } from '@/shared/utils/tailwind';

import { SIDEBAR_TEST_KEYS } from '../config';
import type { NavItemType } from '../types';

type NavItemProps = { className?: string; isNew?: boolean } & NavItemType;

export const NavItem = ({
  icon: Icon,
  title,
  className,
  isNew,
}: NavItemProps) => {
  const { t } = useTranslation(LOCIZE_NAMESPACES.common);

  return (
    <div
      className={cn(
        'flex items-center gap-3 rounded-lg py-2 transition-colors px-3.5',
        className,
      )}
      data-testid={SIDEBAR_TEST_KEYS.navItem}
    >
      <Icon
        className="size-5 text-inherit"
        data-testid={SIDEBAR_TEST_KEYS.navItemIcon}
      />
      <Typography
        tag="span"
        variant={'text-m'}
        data-testid={SIDEBAR_TEST_KEYS.navItemTitle}
        className="text-inherit"
      >
        {title}
      </Typography>
      {isNew && (
        <Badge variant="new" className="h-[1.25rem]">
          <Typography
            variant="text-xs"
            affects={'medium'}
            className="text-inherit"
          >
            {t(LOCIZE_COMMON_KEYS.new).toUpperCase()}
          </Typography>
        </Badge>
      )}
    </div>
  );
};
