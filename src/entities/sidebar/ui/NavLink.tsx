import { Link } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import type { PropsWithChildren } from 'react';

import { SIDEBAR_TEST_KEYS } from '../config';
import type { NavLinkType } from '../types';
import { NavItem } from './NavItem';

export const NavLink = ({
  href,
  isExternal,
  icon,
  title,
  isOpenInNewTab,
  isNew,
}: PropsWithChildren<NavLinkType>) => {
  if (isExternal) {
    return (
      <a
        aria-label={href}
        data-test-is-external={true.toString()}
        data-testid={SIDEBAR_TEST_KEYS.navLink}
        href={href}
        target={isOpenInNewTab ? '_blank' : '_self'}
        rel="noreferrer"
      >
        <NavItem
          icon={icon}
          title={title}
          className="hover:bg-neutral-50 hover:text-primary-black"
        />
      </a>
    );
  }

  return (
    <Link
      data-test-is-external={false.toString()}
      data-testid={SIDEBAR_TEST_KEYS.navLink}
      to={href}
    >
      {({ isActive }) => (
        <NavItem
          isNew={isNew}
          icon={icon}
          title={title}
          className={cn(isActive ? 'bg-stone-50' : 'hover:bg-stone-50')}
        />
      )}
    </Link>
  );
};
