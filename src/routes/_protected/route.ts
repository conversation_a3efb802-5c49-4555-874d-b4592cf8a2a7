import { APP_CONFIG } from '@config/app';
import { ROUTE_NAMES } from '@config/routes';
import { userApi } from '@entities/user';
import { queryOptions } from '@tanstack/react-query';
import { createFileRoute, redirect } from '@tanstack/react-router';

export const Route = createFileRoute('/_protected')({
  beforeLoad: async ({ context: { queryClient }, location }) => {
    const data = await queryClient.ensureQueryData(
      queryOptions({
        queryKey: userApi.useSuspenseUserQuery.getKey(),
        queryFn: userApi.useUserQuery.fetcher(),
      }),
    );

    const isDealsRelatedRoute =
      `/${location.pathname.split('/')[1]}` === ROUTE_NAMES.deals;

    if (!data.me && !isDealsRelatedRoute) {
      throw redirect({
        to: ROUTE_NAMES.auth,
        search: {
          authMethod: APP_CONFIG.authMethods[0],
          redirectUrl: location.pathname,
        },
        replace: true,
      });
    }
  },
});
