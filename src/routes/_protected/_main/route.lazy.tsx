import { ROUTE_NAMES } from '@config/routes';
import { useIsUserAuthorized } from '@entities/user';
import {
  createLazyFileRoute,
  Outlet,
  useLocation,
} from '@tanstack/react-router';
import { lazy } from 'react';

const MainLayout = lazy(() =>
  import('@widgets/layouts/main-layout').then((module) => ({
    default: module.MainLayout,
  })),
);

const HomepageLayout = lazy(() =>
  import('@widgets/layouts/homepage-layout/HomepageLayout').then((module) => ({
    default: module.HomepageLayout,
  })),
);

export const Route = createLazyFileRoute('/_protected/_main')({
  component: () => {
    const { data: isAuthorized } = useIsUserAuthorized();
    const { pathname } = useLocation();

    const isDealsRelatedRoute =
      `/${pathname.split('/')[1]}` === ROUTE_NAMES.deals;
    const shouldRenderHomepageLayout = !isAuthorized && isDealsRelatedRoute;

    const Layout = shouldRenderHomepageLayout ? HomepageLayout : MainLayout;

    return (
      <Layout>
        <Outlet />
      </Layout>
    );
  },
});
