import { sidebarModel } from '@entities/sidebar';
import { DealCategoryName } from '@pages/deals/types';
import { createFileRoute } from '@tanstack/react-router';
import * as z from 'zod';

const dealsSearchSchema = z.object({
  title: z.string().optional(),
  category: z.nativeEnum(DealCategoryName).optional(),
  showNewsletterPopup: z.boolean().optional(),
});

export const Route = createFileRoute('/_protected/_main/deals')({
  validateSearch: dealsSearchSchema,
  loader: () => {
    sidebarModel.events.setIsOpenEv(false);
  },
});
