module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'body-max-line-length': [0, 'always', Number.POSITIVE_INFINITY],
    'subject-case': [2, 'always', ['sentence-case']],
    'subject-max-length': [2, 'always', 72],
    'type-enum': [
      2,
      'always',
      [
        'feat',
        'fix',
        'docs',
        'style',
        'refactor',
        'perf',
        'test',
        'build',
        'ci',
        'chore',
        'revert',
      ],
    ],
    'scope-enum': [
      1,
      'always',
      [
        'auth',
        'deals',
        'payments',
        'agreements',
        'invoices',
        'credit-line',
        'profile',
        'dashboard',
        'ui',
        'api',
        'config',
        'deps',
        'analytics',
        'firebase',
        'signing',
        'grace-period',
        'premium',
        'insurance',
        'standing-payment',
      ],
    ],
  },
};
