{"name": "customer-profile-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "codegen": "graphql-codegen --config codegen.ts", "codegen:watch": "graphql-codegen --config codegen.ts --watch", "test": "vitest", "test:watch": "vitest --watch", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "lint-and-format": "pnpm run lint:fix && pnpm run format", "ts-check": "tsc --noEmit", "git-hooks-install": "lefthook install", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@formkit/auto-animate": "0.8.2", "@hookform/resolvers": "3.7.0", "@radix-ui/react-accordion": "1.2.0", "@radix-ui/react-checkbox": "1.1.1", "@radix-ui/react-dialog": "1.1.1", "@radix-ui/react-dropdown-menu": "2.1.1", "@radix-ui/react-label": "2.1.0", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "1.1.1", "@radix-ui/react-progress": "1.1.0", "@radix-ui/react-select": "2.1.1", "@radix-ui/react-separator": "1.1.0", "@radix-ui/react-slider": "1.2.0", "@radix-ui/react-slot": "1.1.0", "@radix-ui/react-switch": "1.1.0", "@radix-ui/react-tabs": "1.1.0", "@radix-ui/react-toggle": "1.1.0", "@radix-ui/react-toggle-group": "1.1.0", "@radix-ui/react-tooltip": "1.1.2", "@rudderstack/analytics-js": "3.7.11", "@sentry/react": "8.13.0", "@sentry/vite-plugin": "2.20.1", "@tanstack/react-query": "5.49.2", "@tanstack/react-query-devtools": "5.49.2", "@tanstack/react-router": "1.90.0", "@tanstack/router-devtools": "1.90.0", "class-variance-authority": "0.7.0", "clsx": "2.1.1", "compose-function": "3.0.3", "dayjs": "1.11.11", "effector": "23.2.2", "effector-react": "23.2.1", "embla-carousel-auto-scroll": "8.1.6", "embla-carousel-autoplay": "8.1.6", "embla-carousel-fade": "8.5.2", "embla-carousel-react": "8.1.6", "firebase": "11.0.1", "i18next": "23.11.5", "i18next-browser-languagedetector": "8.0.0", "i18next-locize-backend": "6.4.3", "lodash": "4.17.21", "lucide-react": "0.400.0", "next-themes": "0.3.0", "patronum": "2.2.0", "posthog-js": "1.249.4", "react": "18.3.1", "react-dom": "18.3.1", "react-ga4": "^2.1.0", "react-helmet-async": "2.0.5", "react-hook-form": "7.52.1", "react-i18next": "14.1.2", "react-number-format": "5.4.0", "react-use": "17.5.0", "sonner": "1.5.0", "tailwind-merge": "2.3.0", "tailwindcss-animate": "1.0.7", "uuid": "10.0.0", "vite-plugin-pwa": "0.20.5", "vite-plugin-svgr": "4.2.0", "zod": "3.23.8"}, "devDependencies": {"@chromatic-com/storybook": "1.6.0", "@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.16.0", "@faker-js/faker": "8.4.1", "@graphql-codegen/add": "5.0.3", "@graphql-codegen/cli": "5.0.2", "@graphql-codegen/client-preset": "4.3.2", "@graphql-codegen/near-operation-file-preset": "3.0.0", "@graphql-codegen/typescript": "4.0.9", "@graphql-codegen/typescript-operations": "4.2.3", "@graphql-codegen/typescript-react-query": "6.1.0", "@parcel/watcher": "2.4.1", "@savvywombat/tailwindcss-grid-areas": "4.0.0", "@storybook/addon-essentials": "8.1.11", "@storybook/addon-interactions": "8.1.11", "@storybook/addon-links": "8.1.11", "@storybook/addon-onboarding": "8.1.11", "@storybook/blocks": "8.1.11", "@storybook/react": "8.1.11", "@storybook/react-vite": "8.1.11", "@storybook/test": "8.1.11", "@tanstack/eslint-plugin-query": "^5.72.2", "@tanstack/router-vite-plugin": "1.87.13", "@testing-library/jest-dom": "6.4.6", "@testing-library/react": "16.0.0", "@testing-library/user-event": "14.5.2", "@types/compose-function": "0.0.33", "@types/lodash": "4.17.6", "@types/node": "20.14.9", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@types/react-gtm-module": "2.0.3", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitejs/plugin-react-swc": "3.7.0", "@vitest/coverage-v8": "1.6.0", "@vitest/ui": "1.6.0", "autoprefixer": "10.4.19", "eslint": "9.16.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^15.13.0", "jsdom": "24.1.0", "lefthook": "1.6.18", "postcss": "8.4.39", "prettier": "^3.4.2", "storybook": "8.1.11", "tailwindcss": "3.4.4", "typescript": "5.5.3", "unplugin-turbo-console": "1.8.9", "vite": "5.3.3", "vite-plugin-webfont-dl": "3.9.4", "vitest": "1.6.0"}, "packageManager": "pnpm@10.8.0+sha512.0e82714d1b5b43c74610193cb20734897c1d00de89d0e18420aebc5977fa13d780a9cb05734624e81ebd81cc876cd464794850641c48b9544326b5622ca29971"}