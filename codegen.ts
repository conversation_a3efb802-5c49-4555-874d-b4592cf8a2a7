import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: 'https://api.dekker-dev1.ee/graphql',
  documents: ['src/**/*.graphql'],
  config: {
    content:
      '/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */',
    withHooks: true,
    preResolveTypes: true,
    skipTypename: true,
    strictScalars: true,
    scalars: {
      Upload: 'number',
    },
    namingConvention: {
      enumValues: 'change-case-all#upperCase',
      transformUnderscore: true,
    },
  },
  hooks: { afterAllFileWrite: ['pnpm run format'] },
  generates: {
    'src/shared/types/api.gen.ts': {
      plugins: ['add', 'typescript'],
    },
    'src/': {
      preset: 'near-operation-file',
      presetConfig: {
        extension: '.gen.ts',
        baseTypesPath: 'shared/types/api.gen.ts',
      },
      plugins: ['add', 'typescript-operations', 'typescript-react-query'],
      config: {
        // Needed to support the updated React Query 5 API
        reactQueryVersion: 5,
        legacyMode: false,
        exposeFetcher: true,
        exposeQueryKeys: true,
        addSuspenseQuery: true,
        fetcher: '@lib/fetcher#fetcher',
      },
    },
  },
};

export default config;
